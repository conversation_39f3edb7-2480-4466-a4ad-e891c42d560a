{"name": "nextjs-hero<PERSON>-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroui/react": "^2.7.11", "@heroui/theme": "^2.4.17", "@supabase/supabase-js": "^2.52.1", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "framer-motion": "^12.23.3", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "15.3.5", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "supabase": "^2.31.8", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.5", "typescript": "^5"}}